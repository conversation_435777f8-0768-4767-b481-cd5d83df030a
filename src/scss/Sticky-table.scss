$defaultLefts: 0, 4rem, 4rem;

@mixin stickytable($selector, $columns: 1, $lefts: $defaultLefts) {
	#{$selector} {
		tbody {
			font-size: 12px;
		}

		tbody tr:nth-of-type(odd) td {
			background-color: var(--farm-background-base);
		}

		tr td {
			background-color: var(--farm-background-white);
		}

		tr:not(:last-child) td,
		tr th {
			border-bottom: none;
		}

		.v-data-table__wrapper {
			border-top: 1px solid var(--farm-gray-lighten);
			border-bottom: 1px solid var(--farm-gray-lighten);
		}

		tr td:first-child,
		th:first-child {
			&:after {
				content: '';
				position: absolute;
				left: 0;
				bottom: -2px;
				width: 100%;
			}
		}

		&.v-data-table>.v-data-table__wrapper>table {

			>tbody>tr>td:first-child,
			>thead>tr>th:first-child {
				padding-left: 24px;
			}
		}

		@if $columns >0 {
			@for $i from 1 through $columns {

				tr td:nth-child(#{$i}),
				th:nth-child(#{$i}) {
					position: sticky;
					z-index: 4;
					background-color: var(--farm-bw-white);
				}
			}

			@for $i from 1 through $columns {

				tr td:nth-child(#{$i}),
				th:nth-child(#{$i}) {
					left: nth($lefts, $i);
				}
			}
		}

		tr td:first-child,
		tr th:first-child {
			padding-left: 24px;
		}

		th[role='columnheader'] {
			color: var(--farm-secondary-green-darken);
			font-size: 14px;
			font-weight: 700;
			text-transform: uppercase;
		}

		td {
			color: var(--farm-bw-black);
			font-size: 14px;
			font-weight: 400;
		}

		thead {
			height: 51px;
		}

		th[role='columnheader']:has(> .v-data-table__checkbox) {
			max-width: 71px !important;
			padding-left: 24px;
		}

		.v-data-table-header__icon {
			color: var(--farm-gray-base) !important;
			font-size: 12px;
			font-weight: 900;
			margin-left: 8px;
		}

		.v-data-table__checkbox .mdi {
			margin-left: 0;
			font-size: 22px;
			margin-left: -2px;
		}

		.v-input--selection-controls__input {
			width: 20px;
			height: 20px;
			margin-right: 0 !important;
		}
	}
}
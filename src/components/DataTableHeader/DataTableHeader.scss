.header-text {
	position: relative;
	color: var(--farm-secondary-green-darken);
	font-size: 14px;
	font-weight: 700;

	.farm-icon {
		position: absolute;
		right: -20px;
		top: -1px;
		transition: transform 0.5s ease-in-out;

		&.farm-icon--asc {
			transform: rotateX(180deg);
		}
	}
}

th.sortable {
	cursor: pointer;

	.farm-icon::before {
		transition: all 0.3s linear;
	}

	&:not(.active) {
		.farm-icon::before {
			color: var(--farm-bw-black-10) !important;
		}
	}
}

th span.span-checkbox {
	display: flex;
	justify-content: flex-start;
	padding-left: 0;
}

thead {
	height: 51px;
}

th {
	color: var(--farm-secondary-base);
	font-size: 14px;
	font-weight: 700;
	text-transform: uppercase;
	box-shadow: 0px -1px 0px var(--farm-gray-lighten), 0px 1px 0px var(--farm-gray-lighten);
	&.checkbox-container {
		width: 60px !important;
		min-width: 60px !important;
	}
}

:deep(.mdi-sort-reverse-variant)::before {
	color: var(--farm-bw-black-30);
	font-size: 16px;
	font-weight: 400;
}

.farm-icon--desc,
.farm-icon--asc {
	margin-top: 3px;
}

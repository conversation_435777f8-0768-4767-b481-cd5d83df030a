@import '../../configurations/variables';
@import '../../configurations/mixins';
@import '../../configurations/theme-colors';

.farm-typography {
	font-size: 16px;
	color: var(--farm-text-primary);

	&[bold] {
		font-weight: bold;
	}

	&[italic] {
		font-style: italic;
	}

	&[underline] {
		text-decoration: underline;
	}

	&[line-through] {
		text-decoration: line-through;
	}

	&:is(p) {
		margin-bottom: auto;
	}

	&--lighten {
		color: var(--farm-text-secondary);
	}

	&.farm-typography--ellipsis {
		@include ellipsis;
	}

	@each $k in $theme-colors-list {
		&#{'[color=' + $k + ']'} {
			color: var(--farm-#{$k}-base);

			&.farm-typography--lighten {
				color: var(--farm-#{$k}-lighten);
			}

			&.farm-typography--darken {
				color: var(--farm-#{$k}-darken);
			}
		}
	}

	&[color=white] {
		color: white;
	}

	@each $s,
	$val in $fontSizes {
		&--#{$s} {
			font-size: $val;
		}
	}

	@each $w in $fontWeights {
		&--weight-#{$w} {
			font-weight: $w;
		}
	}

}

.farm-typography[color=black] {
	color: var(--farm-bw-black);
	@each $v in $bwVariations {
		&.farm-typography--black-#{$v} {
			color: var(--farm-bw-black-#{$v});
		}
	}
}

@include upToSm {

	@each $s,
	$val in $fontSizes {
		.farm-typography--#{$s} {
			font-size: calc(#{$val} - 2px);
		}
	}
}
@import '../../configurations/variables';
@import '../../configurations/theme-colors';

.farm-icon {
	align-items: center;
	display: inline-flex;
	font-size: 24px;
	justify-content: center;
	letter-spacing: normal;
	line-height: 1;
	position: relative;
	text-indent: 0;

	@each $color in $theme-colors-list {
		&#{'--' + $color} {
			color: var(--farm-#{$color}-base);

			&.farm-icon--lighten {
				color: var(--farm-#{$color}-lighten);
			}

			&.farm-icon--darken {
				color: var(--farm-#{$color}-darken);
			}
		}
	}

	&--white {
		color: white;
	}

	@each $size, $value in $sizes {
		&#{'.farm-icon[size=' + $size + ']'} {
			font-size: $value;
		}
	}

	@each $v in $bwVariations {
		&.farm-icon--black-#{$v} {
			color: var(--farm-bw-black-#{$v});
		}
	}
}

@each $color in $theme-colors-list {
	.farm-btn.farm-btn--icon.farm-btn--#{$color}:not(.farm-btn--disabled)
		.farm-btn__content
		i.mdi.farm-icon--darken {
		color: var(--farm-#{$color}-darken);
	}

	.farm-btn.farm-btn--icon.farm-btn--#{$color}:not(.farm-btn--disabled)
		.farm-btn__content
		i.mdi.farm-icon--lighten {
		color: var(--farm-#{$color}-lighten);
	}
}

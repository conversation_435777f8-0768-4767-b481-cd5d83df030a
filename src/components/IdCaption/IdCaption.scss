@import '../../configurations/mixins';

.idcaption {
	display: flex;
	min-height: 48px;
	max-width: 100%;

	.farm-icon-box {
		margin-right: 8px;
	}

	&.farm-idcaption--noicon .idcaption__body {
		max-width: 100%;
	}

	&__body {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-content: flex-start;
		max-width: calc(100% - 48px);

		&--single {
			justify-content: center;
		}

		.farm-typography {
			margin-bottom: 0;
			display: flex;
			flex: none;
			position: relative;
			align-items: center;
			height: 18px;

			> span {
				max-width: 100%;
				@include ellipsis;
			}
		}

		.farm-tooltip {
			padding-right: 1px;
		}
	}

	&.farm-idcaption--noheight {
		min-height: auto;
	}

	.farm-idcaption--hide-copy-btn {
		.farm-tooltip:has(.mdi-content-copy) {
			display: none;
		}
	}
}

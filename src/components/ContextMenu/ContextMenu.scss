@import '../../configurations/mixins';

.farm-contextmenu {
	display: inline-block;
	flex: 0 1 auto;
}

.farm-contextmenu__popup {
	visibility: hidden;
	opacity: 0;
	padding: 2px;
	position: absolute;
	display: block;
	overflow-y: auto;
	overflow-x: hidden;
	contain: content;
	font-family: 'Manrope', sans-serif !important;

	transform-origin: left top;
	transition: visibility 0.1s linear, opacity 0.1s linear;

	left: 0;

	background: #ffffff;

	@include addShadow;
	border-radius: 5px;

	&--visible {
		opacity: 1;
		visibility: visible;
	}

	&--fixed {
		position: fixed !important;
		top: 50% !important;
		left: 50% !important;
		transform: translate(-50%, -50%) !important;
	}
}
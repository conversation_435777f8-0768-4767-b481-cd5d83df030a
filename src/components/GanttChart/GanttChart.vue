<template>
	<div class="farm-gantt-chart" :style="componentStyle" ref="chartContainer">
		<div class="farm-gantt-chart__header">
			<div class="farm-gantt-chart__row-label-space"/>
			<div class="farm-gantt-chart__timeline" :style="timelineGridStyle">
				<div
					v-for="(month, index) in monthColumns"
					:key="index"
					class="farm-gantt-chart__month-header"
					:class="{ 'farm-gantt-chart__month-header--current': month.isCurrentMonth }"
				>
					<farm-typography
						size="md"
						:weight="500"
						:color="month.isCurrentMonth ? 'primary' : 'black'"
						:color-variation="month.isCurrentMonth ? '' : '50'"
						class="mb-0"
					>
						{{ month.label }}
					</farm-typography>
				</div>
			</div>
		</div>

		<!-- Gantt Chart Content --> 
		<div class="farm-gantt-chart__content">
			<div
				v-for="(group, groupIndex) in data.groups"
				:key="'group-' + groupIndex"
				class="farm-gantt-chart__group"
			>
				<!-- Group label -->
				<div class="farm-gantt-chart__group-label">
					<farm-typography :weight="500">
						{{ group.title }}
					</farm-typography>
				</div>

				<!-- Group timeline with grid and bars -->
				<div class="farm-gantt-chart__group-timeline" :style="timelineGridStyle">
					<!-- Bars positioned using CSS Grid -->
					<div
						v-for="(bar, barIndex) in getPositionedBars(group.bars)"
						:key="'bar-' + barIndex"
						class="farm-gantt-chart__bar"
						:style="getBarGridStyle(bar)"
						@mouseenter="onBarMouseEnter(bar, $event)"
						@mousemove="onBarMouseMove(bar, $event)"
						@mouseleave="onBarMouseLeave"
					>
						<farm-typography size="md" :weight="500" color="white" class="mb-0" ellipsis>
							{{ bar.label }}
						</farm-typography>
					</div>
				</div>
			</div>
		</div>

		<!-- Legend -->
		<div class="farm-gantt-chart__legend" v-if="autoGeneratedLegend.length > 0" :style="legendStyle">
			<div class="farm-gantt-chart__legend-title">
				<farm-typography size="md" :weight="700" color="black" color-variation="50">
					Legenda:
				</farm-typography>
			</div>
			<div
				v-for="(item, index) in autoGeneratedLegend"
				:key="'legend-' + index"
				class="farm-gantt-chart__legend-item"
			>
				<div
					class="farm-gantt-chart__legend-color"
					:style="{ backgroundColor: item.color }"
				></div>
				<div class="farm-gantt-chart__legend-label">
					<farm-typography size="md" color="black" color-variation="50">
						{{ item.label }}
					</farm-typography>
				</div>
			</div>
		</div>

		<!-- Enhanced Tooltip Container with Priority System -->
		<div
			v-if="tooltipState.visible"
			class="farm-gantt-chart__tooltip-container"
			:style="tooltipPositionStyle"
		>
			<!-- Priority 1: Slot-based tooltip (highest priority) -->
			<div v-if="$slots.tooltip" class="farm-gantt-chart__tooltip">
				<slot
					name="tooltip"
					:bar="tooltipState.barData"
					:tooltipData="tooltipState.barData && tooltipState.barData.tooltipData"
				/>
			</div>
			
			<!-- Priority 2: Structured tooltipData (medium priority) -->
			<div
				v-else-if="tooltipState.barData && tooltipState.barData.tooltipData"
				class="farm-gantt-chart__tooltip farm-tooltip__popup farm-tooltip__popup--visible"
			>
				<div class="farm-tooltip__header">
					<div class="farm-tooltip__title">
						{{ tooltipState.title }}
					</div>
				</div>
				<div class="farm-tooltip__content">
					<div
						v-for="(value, key) in tooltipState.barData.tooltipData"
						:key="key"
						class="tooltip-data-row"
					>
						<span class="tooltip-label">{{ key }}:</span>
						<span class="tooltip-value">{{ value }}</span>
					</div>
				</div>
			</div>
			
			<!-- Priority 3: Basic fallback (lowest priority) -->
			<div
				v-else
				class="farm-gantt-chart__tooltip farm-tooltip__popup farm-tooltip__popup--visible"
			>
				<div class="farm-tooltip__header">
					<div class="farm-tooltip__title">
						<strong>{{ tooltipState.title }}</strong>
					</div>
				</div>
				<div class="farm-tooltip__content">
					<p>{{ tooltipState.barData ? formatDateRange(tooltipState.barData.start, tooltipState.barData.end) : '' }}</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType, computed, reactive, ref } from 'vue';
import type { GanttData, TooltipState, GanttBar } from './types';
import { getMonthsBetween, formatMonth, isCurrentMonth, getColumnForDate, formatDateRange } from './utils/dateUtils';
import { buildGanttData, buildBarPositioning } from './composition';

export default defineComponent({
	name: 'farm-gantt-chart',
	props: {
		data: {
			type: Object as PropType<GanttData>,
			required: true,
		},
	},
	emits: [],
	setup(props) {
		// NEW: Enhanced tooltip state management
		const tooltipState = reactive<TooltipState>({
			visible: false,
			x: 0,
			y: 0,
			title: '',
			barData: null // Now stores complete bar object
		});

		const chartContainer = ref<HTMLElement>();
		// SECTION: Data Processing & Range Calculation
		const { autoCalculatedDateRange, autoGeneratedLegend } = buildGanttData(props);

		// SECTION: Timeline Generation
		const monthColumns = computed(() => {
			const { start, end } = autoCalculatedDateRange.value;
			const months = getMonthsBetween(start, end);
			return months.map(month => ({
				date: month,
				label: formatMonth(month),
				isCurrentMonth: isCurrentMonth(month),
			}));
		});

		const timelineGridStyle = computed(() => ({
			gridTemplateColumns: `repeat(${monthColumns.value.length}, 80px)`,
		}));

		const todayColumn = computed(() => {
			const today = new Date();
			const { start } = autoCalculatedDateRange.value;
			const column = getColumnForDate(today, start);
			return column >= 0 && column < monthColumns.value.length ? column : -1;
		});

		// SECTION: Bar Positioning & Styling
		const { getBarGridStyle, getPositionedBars } = buildBarPositioning(
			autoCalculatedDateRange,
			monthColumns
		);

		// SECTION: Layout & Styling
		const contentHeight = computed(() => {
			let totalHeight = 0;
			props.data.groups.forEach(group => {
				const positionedBars = getPositionedBars(group.bars);
				const maxRows = Math.max(
					1,
					...positionedBars.map(bar => (bar.rowPosition || 0) + 1)
				);
				const groupHeight = Math.max(60, maxRows * 35 + 10);
				totalHeight += groupHeight + 20;
			});
			return totalHeight;
		});

		const componentStyle = computed(() => ({
			'--gantt-content-height': `${contentHeight.value}px`,
		}));

		const legendStyle = computed(() => {
			const timelineWidth = monthColumns.value.length * 80;
			const totalWidth = 120 + timelineWidth;
			return {
				width: `${totalWidth}px`,
			};
		});

		// NEW: Enhanced mouse event handlers with proper typing
		const onBarMouseEnter = (bar: GanttBar, event: MouseEvent) => {
			tooltipState.visible = true;
			tooltipState.title = bar.label;
			tooltipState.barData = bar; // Store complete bar object
			updateTooltipPosition(event);
		};

		const onBarMouseMove = (bar: GanttBar, event: MouseEvent) => {
			if (tooltipState.visible) {
				updateTooltipPosition(event);
			}
		};

		const onBarMouseLeave = () => {
			tooltipState.visible = false;
			tooltipState.barData = null;
		};

		// NEW: Position calculation utility
		const updateTooltipPosition = (event: MouseEvent) => {
			if (!chartContainer.value) return;

			const containerRect = chartContainer.value.getBoundingClientRect();
			const x = event.clientX - containerRect.left;
			const y = event.clientY - containerRect.top;

			// Boundary detection
			const tooltipWidth = 300; // Approximate tooltip width
			const tooltipHeight = 100; // Approximate tooltip height
			const margin = 15; // Margin from cursor

			// Position tooltip below the cursor by default
			let tooltipX = x + margin;
			let tooltipY = y + margin;

			// Check right boundary and adjust if needed
			if (tooltipX + tooltipWidth > containerRect.width) {
				tooltipX = Math.max(10, containerRect.width - tooltipWidth - 10);
			}

			// Check bottom boundary - if tooltip goes beyond bottom, position it above cursor
			if (tooltipY + tooltipHeight > containerRect.height) {
				tooltipY = Math.max(10, y - tooltipHeight - margin);
			}

			// Ensure tooltip doesn't go beyond left boundary
			tooltipX = Math.max(10, tooltipX);
			
			// Ensure tooltip doesn't go beyond top boundary
			tooltipY = Math.max(10, tooltipY);

			tooltipState.x = tooltipX;
			tooltipState.y = tooltipY;
		};

		// NEW: Tooltip positioning style
		const tooltipPositionStyle = computed(() => ({
			transform: `translate(${tooltipState.x}px, ${tooltipState.y}px)`,
			zIndex: 999
		}));

		return {
			monthColumns,
			timelineGridStyle,
			todayColumn,
			autoGeneratedLegend,
			legendStyle,
			getBarGridStyle,
			getPositionedBars,
			componentStyle,
			// NEW: Tooltip-related returns
			tooltipState,
			tooltipPositionStyle,
			chartContainer,
			onBarMouseEnter,
			onBarMouseMove,
			onBarMouseLeave,
			formatDateRange, // Importada do dateUtils
		};
	},
});
</script>

<style lang="scss" scoped>
@import './GanttChart';
</style>

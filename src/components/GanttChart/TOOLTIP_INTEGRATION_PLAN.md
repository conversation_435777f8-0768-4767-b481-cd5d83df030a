# GanttChart Tooltip Integration - Solution Architecture

## 🔍 **Problem Analysis**

Based on my analysis of the current implementation, I've identified the core issue:

**Coordinate System Conflict**: The existing [`Tooltip`](../Tooltip/Tooltip.vue:165) component uses `document.querySelector('body').appendChild(popup.value)` (line 165) which positions tooltips relative to the document body. However, the [`GanttChart`](GanttChart.vue:2) uses CSS Grid within a scrollable container (`overflow-x: auto`, `overflow-y: auto` in [`GanttChart.scss`](GanttChart.scss:11-12)), creating a coordinate system mismatch.

## 🏗️ **Solution Architecture**

### **1. Component Architecture Diagram**

```mermaid
graph TD
    A[GanttChart Container] --> B[GanttChart Header]
    A --> C[GanttChart Content]
    A --> D[GanttChart Legend]
    A --> E[**NEW: Scoped Tooltip Container**]
    
    C --> F[Group Labels]
    C --> G[Group Timelines]
    
    G --> H[Gantt Bars with Event Handlers]
    H --> I[**NEW: Mouse Events**]
    I --> J[mousemove]
    I --> K[mouseenter]
    I --> L[mouseleave]
    
    E --> M[**NEW: Positioned Tooltip**]
    M --> N[Tooltip Header - Bold Title]
    M --> O[Tooltip Content - Placeholder]
    
    style E fill:#e1f5fe
    style M fill:#e1f5fe
    style I fill:#fff3e0
    style J fill:#fff3e0
    style K fill:#fff3e0
    style L fill:#fff3e0
```

### **2. Data Flow Design**

```mermaid
sequenceDiagram
    participant User
    participant Bar as Gantt Bar
    participant Chart as GanttChart
    participant Tooltip as Scoped Tooltip
    
    User->>Bar: Hover (mouseenter)
    Bar->>Chart: Emit hover event with bar data
    Chart->>Chart: Calculate tooltip position relative to scroll container
    Chart->>Tooltip: Show tooltip with positioning
    
    User->>Bar: Mouse move within bar
    Bar->>Chart: Emit mousemove event with coordinates
    Chart->>Chart: Update tooltip position (cursor following)
    Chart->>Tooltip: Update position
    
    User->>Bar: Leave bar (mouseleave)  
    Bar->>Chart: Emit leave event
    Chart->>Tooltip: Hide tooltip
```

### **3. Technical Architecture**

#### **3.1 Scoped Tooltip Container Design**
Instead of using the existing [`Tooltip`](../Tooltip/Tooltip.vue:165) component's body portal approach, I'll design a scoped container within the GanttChart:

```typescript
// New tooltip state management
interface TooltipState {
  visible: boolean;
  x: number;
  y: number;
  title: string;
  content: string;
  barData: GanttBar | null;
}
```

#### **3.2 Positioning Strategy**
The key innovation is positioning relative to the **scroll container** rather than the document body:

1. **Container-Relative Coordinates**: Calculate mouse position relative to the [`farm-gantt-chart`](GanttChart.vue:2) container
2. **Transform-Based Positioning**: Use `transform: translate()` for high-performance positioning
3. **Boundary Detection**: Ensure tooltip stays within the scrollable area bounds

#### **3.3 Event Handler Integration**
Replace the existing [`@click="$emit('bar-click', bar)"`](GanttChart.vue:47) with comprehensive mouse event handlers:

```vue
<div
  v-for="(bar, barIndex) in getPositionedBars(group.bars)"
  :key="'bar-' + barIndex"
  class="farm-gantt-chart__bar"
  :style="getBarGridStyle(bar)"
  @mouseenter="onBarMouseEnter(bar, $event)"
  @mousemove="onBarMouseMove(bar, $event)"
  @mouseleave="onBarMouseLeave"
>
```

### **4. SCSS Integration Strategy**

#### **4.1 Z-Index Hierarchy**
```scss
.farm-gantt-chart {
  // Current z-index values from analysis:
  // - Header: z-index: 2
  // - Month headers: z-index: 1  
  // - Bars: z-index: 2
  
  &__tooltip-container {
    position: fixed; // Key: fixed relative to scroll container
    z-index: 999; // Above all chart elements
    pointer-events: none; // Prevent tooltip from interfering with hover
  }
  
  &__tooltip {
    // Reuse existing Tooltip styles with scoped positioning
    @extend .farm-tooltip__popup;
    position: relative; // Not absolute since container handles positioning
    pointer-events: auto; // Allow interaction if needed
  }
}
```

#### **4.2 Preserve Existing Bar Hover Effects**
Maintain the current [`&:hover`](GanttChart.scss:126-129) effects:
```scss
&__bar {
  // Current hover effects preserved:
  &:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    transform: translateY(-2px);
  }
}
```

### **5. Implementation Plan**

#### **Phase 1: Tooltip State Management**
1. Add tooltip reactive state to [`GanttChart.vue`](GanttChart.vue:98) setup function
2. Create tooltip position calculation utilities
3. Add boundary detection logic

#### **Phase 2: Event Handler Integration**  
1. Replace [`@click`](GanttChart.vue:47) with mouse event handlers
2. Implement cursor-following position updates
3. Add debouncing for performance optimization

#### **Phase 3: Scoped Tooltip Component**
1. Create scoped tooltip container in template
2. Implement transform-based positioning 
3. Add placeholder content with bold title formatting

#### **Phase 4: SCSS Updates**
1. Add tooltip container styles to [`GanttChart.scss`](GanttChart.scss)
2. Ensure z-index hierarchy is correct
3. Preserve existing bar hover effects

#### **Phase 5: Testing & Optimization**
1. Test scroll synchronization preservation
2. Verify performance with multiple bars
3. Test edge cases (tooltip near boundaries)

### **6. Performance Considerations**

#### **6.1 Optimization Strategies**
1. **Event Debouncing**: Limit mousemove event frequency to 60fps
2. **Calculation Caching**: Cache container bounds to avoid repeated DOM queries
3. **Transform Over Left/Top**: Use `transform: translate()` for better performance
4. **Single Tooltip Instance**: Reuse one tooltip for all bars instead of creating multiple

#### **6.2 Scroll Synchronization Preservation**
The scoped approach ensures the [`overflow-x: auto`](GanttChart.scss:11) behavior remains intact since tooltips are positioned within the same coordinate system.

### **7. Vue 2 Compatibility Strategy**

Since Vue 2 doesn't have Teleport, the scoped container approach is optimal:
- No need for portal behavior
- Direct parent-child relationship maintained
- Simpler event handling and state management

### **8. Integration with Existing Tooltip Component**

While we can't use the existing [`Tooltip`](../Tooltip/Tooltip.vue) component directly due to its body portal behavior, we'll reuse its styling and structure:

```vue
<!-- Reuse Tooltip styles but with scoped positioning -->
<div 
  v-if="tooltipState.visible"
  class="farm-tooltip__popup farm-tooltip__popup--visible farm-tooltip__popup--fluid"
  :style="tooltipPositionStyle"
>
  <div class="farm-tooltip__header">
    <div class="farm-tooltip__title" style="font-weight: bold;">
      {{ tooltipState.title }}
    </div>
  </div>
  <div class="farm-tooltip__content">
    {{ tooltipState.content }}
  </div>
</div>
```

### **9. Detailed Implementation Code Structure**

#### **9.1 Vue Component Updates**

```typescript
// Add to GanttChart.vue setup function
import { ref, reactive, computed } from 'vue';

interface TooltipState {
  visible: boolean;
  x: number;
  y: number;
  title: string;
  content: string;
  barData: GanttBar | null;
}

export default defineComponent({
  setup(props) {
    // Existing setup code...
    
    // NEW: Tooltip state management
    const tooltipState = reactive<TooltipState>({
      visible: false,
      x: 0,
      y: 0,
      title: '',
      content: 'Placeholder content - será implementado posteriormente',
      barData: null
    });
    
    const chartContainer = ref<HTMLElement>();
    
    // NEW: Mouse event handlers
    const onBarMouseEnter = (bar: GanttBar, event: MouseEvent) => {
      tooltipState.visible = true;
      tooltipState.title = bar.label;
      tooltipState.barData = bar;
      updateTooltipPosition(event);
    };
    
    const onBarMouseMove = (bar: GanttBar, event: MouseEvent) => {
      if (tooltipState.visible) {
        updateTooltipPosition(event);
      }
    };
    
    const onBarMouseLeave = () => {
      tooltipState.visible = false;
      tooltipState.barData = null;
    };
    
    // NEW: Position calculation utility
    const updateTooltipPosition = (event: MouseEvent) => {
      if (!chartContainer.value) return;
      
      const containerRect = chartContainer.value.getBoundingClientRect();
      const x = event.clientX - containerRect.left;
      const y = event.clientY - containerRect.top;
      
      // Boundary detection
      const tooltipWidth = 300; // Approximate tooltip width
      const tooltipHeight = 100; // Approximate tooltip height
      
      tooltipState.x = Math.min(x, containerRect.width - tooltipWidth);
      tooltipState.y = Math.max(y - tooltipHeight - 10, 10); // 10px margin
    };
    
    // NEW: Tooltip positioning style
    const tooltipPositionStyle = computed(() => ({
      transform: `translate(${tooltipState.x}px, ${tooltipState.y}px)`,
      zIndex: 999
    }));
    
    return {
      // Existing returns...
      tooltipState,
      tooltipPositionStyle,
      chartContainer,
      onBarMouseEnter,
      onBarMouseMove,
      onBarMouseLeave
    };
  }
});
```

#### **9.2 Template Updates**

```vue
<template>
  <div class="farm-gantt-chart" :style="componentStyle" ref="chartContainer">
    <!-- Existing header and content... -->
    
    <!-- Updated bar template with mouse events -->
    <div
      v-for="(bar, barIndex) in getPositionedBars(group.bars)"
      :key="'bar-' + barIndex"
      class="farm-gantt-chart__bar"
      :style="getBarGridStyle(bar)"
      @mouseenter="onBarMouseEnter(bar, $event)"
      @mousemove="onBarMouseMove(bar, $event)"
      @mouseleave="onBarMouseLeave"
    >
      <farm-typography size="md" :weight="500" color="white" class="mb-0" ellipsis>
        {{ bar.label }}
      </farm-typography>
    </div>
    
    <!-- NEW: Scoped Tooltip Container -->
    <div 
      v-if="tooltipState.visible"
      class="farm-gantt-chart__tooltip-container"
      :style="tooltipPositionStyle"
    >
      <div class="farm-gantt-chart__tooltip farm-tooltip__popup farm-tooltip__popup--visible farm-tooltip__popup--fluid">
        <div class="farm-tooltip__header">
          <div class="farm-tooltip__title">
            <strong>{{ tooltipState.title }}</strong>
          </div>
        </div>
        <div class="farm-tooltip__content">
          {{ tooltipState.content }}
        </div>
      </div>
    </div>
  </div>
</template>
```

#### **9.3 SCSS Updates**

```scss
// Add to GanttChart.scss
.farm-gantt-chart {
  // Existing styles...
  
  // NEW: Tooltip container styles
  &__tooltip-container {
    position: absolute;
    z-index: 999;
    pointer-events: none;
    top: 0;
    left: 0;
  }
  
  &__tooltip {
    pointer-events: auto;
    position: relative;
    
    // Import existing tooltip styles
    background-color: #333333;
    color: #f5f5f5;
    border-radius: 5px;
    font-family: 'Manrope', sans-serif !important;
    font-size: 12px;
    font-weight: 500;
    padding: 16px;
    width: auto;
    max-width: 300px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    
    .farm-tooltip__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .farm-tooltip__title {
      font-weight: 600;
      font-size: 13px;
    }
    
    .farm-tooltip__content {
      font-weight: 500;
    }
  }
}
```

## 📋 **Implementation Checklist**

- [ ] Add tooltip state management to GanttChart setup
- [ ] Create position calculation utilities  
- [ ] Replace bar click handlers with mouse event handlers
- [ ] Implement cursor-following positioning logic
- [ ] Add scoped tooltip container to template
- [ ] Update SCSS with tooltip styles and z-index hierarchy
- [ ] Add boundary detection for tooltip positioning
- [ ] Test scroll behavior preservation
- [ ] Optimize performance with debouncing
- [ ] Add placeholder content with bold title formatting

## 🚀 **Next Steps**

1. **Review Architecture**: Confirm this approach meets all requirements
2. **Switch to Code Mode**: Use `switch_mode` to begin implementation
3. **Iterative Development**: Implement phase by phase with testing
4. **Performance Testing**: Verify smooth scrolling and hover performance
5. **Future Content Integration**: Plan for dynamic tooltip content implementation

This architecture solves the coordinate system conflict by keeping tooltips within the GanttChart's coordinate system while maintaining all existing functionality and performance characteristics.

---

**Key Benefits of This Approach:**
- ✅ Resolves coordinate system conflict
- ✅ Preserves horizontal scroll synchronization  
- ✅ Maintains existing visual effects and performance
- ✅ Vue 2 compatible (no teleport needed)
- ✅ Cursor-following behavior as requested
- ✅ Reuses existing Tooltip component styling
- ✅ Placeholder content ready for future enhancement
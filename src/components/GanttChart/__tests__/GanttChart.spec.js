import { shallowMount } from '@vue/test-utils';
import GanttChart from '../GanttChart.vue';

describe('GanttChart component', () => {
	let wrapper;
	let component;

	const defaultProps = {
		data: {
			groups: [
				{
					title: 'Test Group',
					bars: [
						{
							id: 1,
							label: 'Test Bar',
							start: new Date(2025, 0, 1),
							end: new Date(2025, 1, 1),
							color: '#7BC4F7',
						},
					],
				},
			],
		},
	};

	beforeEach(() => {
		wrapper = shallowMount(GanttChart, {
			propsData: defaultProps,
		});
		component = wrapper.vm;
	});

	test('Created hook', () => {
		expect(wrapper).toBeDefined();
	});

	describe('mount component', () => {
		it('renders correctly', () => {
			expect(wrapper.element).toMatchSnapshot();
		});

		it('renders groups correctly', () => {
			expect(wrapper.findAll('.farm-gantt-chart__group')).toHaveLength(1);
		});

		it('renders month headers', () => {
			const monthHeaders = wrapper.findAll('.farm-gantt-chart__month-header');
			expect(monthHeaders.length).toBeGreaterThan(0);
		});

		it('displays group titles correctly', () => {
			const groupLabel = wrapper.find('.farm-gantt-chart__group-label');
			expect(groupLabel.text()).toContain('Test Group');
		});
	});

	describe('Props', () => {
		it('accepts data prop with new structure', () => {
			expect(component.data).toEqual(defaultProps.data);
		});

		it('validates required data prop structure', () => {
			expect(component.data.groups).toBeDefined();
			expect(Array.isArray(component.data.groups)).toBe(true);
		});

		it('validates group structure', () => {
			const group = component.data.groups[0];
			expect(group.title).toBeDefined();
			expect(group.bars).toBeDefined();
			expect(Array.isArray(group.bars)).toBe(true);
		});

		it('validates bar structure', () => {
			const bar = component.data.groups[0].bars[0];
			expect(bar.id).toBeDefined();
			expect(bar.label).toBeDefined();
			expect(bar.start).toBeDefined();
			expect(bar.end).toBeDefined();
			expect(bar.color).toBeDefined();
		});
	});

	describe('Automatic Calculations', () => {
		describe('Date Range Calculation', () => {
			it('should calculate date range automatically from bars', () => {
				const testData = {
					groups: [
						{
							title: 'Group 1',
							bars: [
								{
									id: 1,
									label: 'Early Bar',
									start: new Date(2025, 0, 15), // Jan 15, 2025
									end: new Date(2025, 2, 10), // Mar 10, 2025
									color: '#7BC4F7',
								},
								{
									id: 2,
									label: 'Late Bar',
									start: new Date(2025, 4, 5), // May 5, 2025
									end: new Date(2025, 6, 20), // Jul 20, 2025
									color: '#8BB455',
								},
							],
						},
					],
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData },
				});

				// Should calculate from January 1st to July 31st (full months)
				const monthColumns = testWrapper.vm.monthColumns;
				expect(monthColumns.length).toBe(7); // Jan to Jul = 7 months
				expect(monthColumns[0].label).toContain('Jan');
				expect(monthColumns[monthColumns.length - 1].label).toContain('Jul');
			});

			it('should handle single month range', () => {
				const testData = {
					groups: [
						{
							title: 'Single Month Group',
							bars: [
								{
									id: 1,
									label: 'Single Month Bar',
									start: new Date(2025, 3, 5), // Apr 5, 2025
									end: new Date(2025, 3, 25), // Apr 25, 2025
									color: '#7BC4F7',
								},
							],
						},
					],
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData },
				});

				const monthColumns = testWrapper.vm.monthColumns;
				expect(monthColumns.length).toBe(1);
				expect(monthColumns[0].label).toContain('Abr');
			});
		});

		describe('Legend Generation', () => {
			it('should generate legend automatically from unique colors and labels', () => {
				const testData = {
					groups: [
						{
							title: 'Group 1',
							bars: [
								{
									id: 1,
									label: 'Design',
									start: new Date(2025, 0, 1),
									end: new Date(2025, 1, 1),
									color: '#8E44AD',
								},
								{
									id: 2,
									label: 'Development',
									start: new Date(2025, 1, 1),
									end: new Date(2025, 2, 1),
									color: '#16A085',
								},
								{
									id: 3,
									label: 'Design', // Same label, same color - should not duplicate
									start: new Date(2025, 2, 1),
									end: new Date(2025, 3, 1),
									color: '#8E44AD',
								},
							],
						},
					],
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData },
				});

				const legend = testWrapper.vm.autoGeneratedLegend;
				expect(legend).toHaveLength(2); // Only unique combinations
				expect(legend.some(item => item.label === 'Design' && item.color === '#8E44AD')).toBe(true);
				expect(legend.some(item => item.label === 'Development' && item.color === '#16A085')).toBe(true);
			});

			it('should display generated legend in template', () => {
				const legendItems = wrapper.findAll('.farm-gantt-chart__legend-item');
				expect(legendItems.length).toBeGreaterThan(0);
			});
		});
	});

	describe('Methods', () => {
		describe('getPositionedBars', () => {
			it('should position bars correctly', () => {
				const bars = [
					{
						id: 1,
						start: new Date(2025, 0, 1),
						end: new Date(2025, 1, 1),
						label: 'Bar 1',
						color: '#7BC4F7',
					},
					{
						id: 2,
						start: new Date(2025, 0, 15),
						end: new Date(2025, 1, 15),
						label: 'Bar 2',
						color: '#8BB455',
					},
				];
				const positionedBars = component.getPositionedBars(bars);
				expect(positionedBars).toHaveLength(2);
				expect(positionedBars[0].rowPosition).toBeDefined();
				expect(positionedBars[1].rowPosition).toBeDefined();
			});

			it('should handle empty bars array', () => {
				const positionedBars = component.getPositionedBars([]);
				expect(positionedBars).toEqual([]);
			});

			it('should handle invalid dates gracefully', () => {
				const bars = [
					{
						id: 1,
						start: 'invalid-date',
						end: 'invalid-date',
						label: 'Invalid Bar',
						color: '#7BC4F7',
					},
				];
				const positionedBars = component.getPositionedBars(bars);
				expect(positionedBars).toHaveLength(1);
				expect(positionedBars[0].rowPosition).toBeDefined();
			});
		});

		describe('getBarGridStyle', () => {
			it('should return correct grid style for bars', () => {
				const bar = {
					id: 1,
					start: new Date(2025, 0, 1),
					end: new Date(2025, 1, 1),
					label: 'Test Bar',
					color: '#7BC4F7',
					rowPosition: 0,
				};
				const style = component.getBarGridStyle(bar);
				expect(style['background-color']).toBe('#7BC4F7');
				expect(style['grid-row']).toBe('1');
				expect(style['grid-column-start']).toBeDefined();
				expect(style['grid-column-end']).toBeDefined();
			});

			// it('should handle invalid dates in bar style calculation', () => {
			// 	const bar = {
			// 		id: 1,
			// 		start: 'invalid',
			// 		end: 'invalid',
			// 		label: 'Invalid Bar',
			// 		color: '#FF0000',
			// 		rowPosition: 0,
			// 	};
			// 	const style = component.getBarGridStyle(bar);
			// 	expect(style['background-color']).toBe('#FF0000');
			// 	expect(style.gridColumn).toBe('1 / 2');
			// });
		});
	});

	describe('Events', () => {
		it('should emit bar-click event when bar is clicked', async () => {
			const bar = wrapper.find('.farm-gantt-chart__bar');
			if (bar.exists()) {
				await bar.trigger('click');
				expect(wrapper.emitted('bar-click')).toBeTruthy();
				expect(wrapper.emitted('bar-click')[0][0]).toEqual(
					expect.objectContaining({
						id: 1,
						label: 'Test Bar',
						color: '#7BC4F7',
					})
				);
			}
		});
	});

	describe('Tooltip System (Critical)', () => {
		let mockEvent;

		beforeEach(() => {
			mockEvent = {
				clientX: 100,
				clientY: 200
			};

			// Mock getBoundingClientRect
			const mockRect = {
				left: 0,
				top: 0,
				width: 800,
				height: 600
			};
			
			// Create a mock element
			const mockElement = {
				getBoundingClientRect: jest.fn(() => mockRect)
			};
			
			// Mock the ref
			component.$refs = { chartContainer: mockElement };
			component.chartContainer = mockElement;
		});

		describe('Mouse Events', () => {
			it('should show tooltip on bar mouse enter', () => {
				const testBar = {
					id: 1,
					label: 'Test Bar',
					start: new Date(2025, 0, 1),
					end: new Date(2025, 1, 1),
					color: '#7BC4F7'
				};

				component.onBarMouseEnter(testBar, mockEvent);

				expect(component.tooltipState.visible).toBe(true);
				expect(component.tooltipState.title).toBe('Test Bar');
				expect(component.tooltipState.barData).toEqual(testBar);
			});

			it('should hide tooltip on bar mouse leave', () => {
				// First show tooltip
				const testBar = { id: 1, label: 'Test', start: new Date(), end: new Date(), color: '#fff' };
				component.onBarMouseEnter(testBar, mockEvent);
				expect(component.tooltipState.visible).toBe(true);

				// Then hide it
				component.onBarMouseLeave();
				expect(component.tooltipState.visible).toBe(false);
				expect(component.tooltipState.barData).toBe(null);
			});

			it('should update tooltip position on mouse move', () => {
				const testBar = { id: 1, label: 'Test', start: new Date(), end: new Date(), color: '#fff' };
				component.onBarMouseEnter(testBar, mockEvent);

				const newEvent = { clientX: 150, clientY: 250 };
				component.onBarMouseMove(testBar, newEvent);

				expect(component.tooltipState.x).toBeGreaterThan(0);
				expect(component.tooltipState.y).toBeGreaterThan(0);
			});
		});

		describe('Tooltip Positioning', () => {
			it('should position tooltip with margin from cursor', () => {
				const testBar = { id: 1, label: 'Test', start: new Date(), end: new Date(), color: '#fff' };
				component.onBarMouseEnter(testBar, mockEvent);

				// Should add margin to cursor position
				expect(component.tooltipState.x).toBe(115); // 100 + 15 margin
				expect(component.tooltipState.y).toBe(215); // 200 + 15 margin
			});

			it('should handle right boundary detection', () => {
				const rightEdgeEvent = { clientX: 750, clientY: 200 };
				const testBar = { id: 1, label: 'Test', start: new Date(), end: new Date(), color: '#fff' };
				
				component.onBarMouseEnter(testBar, rightEdgeEvent);

				// Should not exceed container width minus tooltip width
				expect(component.tooltipState.x).toBeLessThanOrEqual(490); // 800 - 300 - 10
			});

			it('should handle bottom boundary detection', () => {
				const bottomEdgeEvent = { clientX: 100, clientY: 550 };
				const testBar = { id: 1, label: 'Test', start: new Date(), end: new Date(), color: '#fff' };
				
				component.onBarMouseEnter(testBar, bottomEdgeEvent);

				// Should position above cursor when near bottom
				expect(component.tooltipState.y).toBeLessThan(550);
			});

			it('should respect minimum positioning boundaries', () => {
				const edgeEvent = { clientX: 5, clientY: 5 };
				const testBar = { id: 1, label: 'Test', start: new Date(), end: new Date(), color: '#fff' };
				
				component.onBarMouseEnter(testBar, edgeEvent);

				// Should not go below minimum boundaries
				expect(component.tooltipState.x).toBeGreaterThanOrEqual(10);
				expect(component.tooltipState.y).toBeGreaterThanOrEqual(10);
			});
		});

		describe('Tooltip Content Priority System', () => {
			it('should prioritize slot tooltip (Priority 1)', async () => {
				const wrapperWithSlot = shallowMount(GanttChart, {
					propsData: defaultProps,
					slots: {
						tooltip: '<div class="custom-tooltip">Custom Tooltip Content</div>'
					}
				});

				const testBar = { id: 1, label: 'Test', start: new Date(), end: new Date(), color: '#fff' };
				wrapperWithSlot.vm.tooltipState.visible = true;
				wrapperWithSlot.vm.tooltipState.barData = testBar;

				await wrapperWithSlot.vm.$nextTick();

				expect(wrapperWithSlot.find('.custom-tooltip').exists()).toBe(true);
			});

			it('should use structured tooltipData (Priority 2)', async () => {
				const testData = {
					groups: [{
						title: 'Test Group',
						bars: [{
							id: 1,
							label: 'Test Bar',
							start: new Date(2025, 0, 1),
							end: new Date(2025, 1, 1),
							color: '#7BC4F7',
							tooltipData: {
								'Taxa': '1.5%',
								'Status': 'Ativo'
							}
						}]
					}]
				};

				const wrapperWithTooltipData = shallowMount(GanttChart, {
					propsData: { data: testData }
				});

				wrapperWithTooltipData.vm.tooltipState.visible = true;
				wrapperWithTooltipData.vm.tooltipState.barData = testData.groups[0].bars[0];
				wrapperWithTooltipData.vm.tooltipState.title = 'Test Bar';

				await wrapperWithTooltipData.vm.$nextTick();

				expect(wrapperWithTooltipData.find('.tooltip-data-row').exists()).toBe(true);
			});

			it('should fallback to basic tooltip (Priority 3)', async () => {
				const testBar = { id: 1, label: 'Test Bar', start: new Date(2025, 0, 1), end: new Date(2025, 1, 1), color: '#7BC4F7' };
				
				component.tooltipState.visible = true;
				component.tooltipState.barData = testBar;
				component.tooltipState.title = 'Test Bar';

				await wrapper.vm.$nextTick();

				expect(wrapper.find('.farm-tooltip__title').exists()).toBe(true);
			});
		});
	});

	describe('Edge Cases (Critical for Production)', () => {
		describe('Invalid Date Handling', () => {
			it('should handle bars with invalid start dates', () => {
				const testData = {
					groups: [{
						title: 'Invalid Dates Group',
						bars: [{
							id: 1,
							label: 'Invalid Start Bar',
							start: 'invalid-date',
							end: new Date(2025, 1, 1),
							color: '#7BC4F7'
						}]
					}]
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData }
				});

				expect(() => testWrapper.vm.monthColumns).not.toThrow();
				expect(testWrapper.vm.monthColumns.length).toBeGreaterThan(0);
			});

			it('should handle bars with invalid end dates', () => {
				const testData = {
					groups: [{
						title: 'Invalid Dates Group',
						bars: [{
							id: 1,
							label: 'Invalid End Bar',
							start: new Date(2025, 0, 1),
							end: 'invalid-date',
							color: '#7BC4F7'
						}]
					}]
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData }
				});

				expect(() => testWrapper.vm.getPositionedBars(testData.groups[0].bars)).not.toThrow();
			});

			it('should handle bars with null/undefined dates', () => {
				const testData = {
					groups: [{
						title: 'Null Dates Group',
						bars: [{
							id: 1,
							label: 'Null Dates Bar',
							start: null,
							end: undefined,
							color: '#7BC4F7'
						}]
					}]
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData }
				});

				expect(() => testWrapper.vm.getBarGridStyle(testData.groups[0].bars[0])).not.toThrow();
			});

			it('should handle start date after end date', () => {
				const testData = {
					groups: [{
						title: 'Reversed Dates Group',
						bars: [{
							id: 1,
							label: 'Reversed Dates Bar',
							start: new Date(2025, 2, 1), // March
							end: new Date(2025, 0, 1),   // January
							color: '#7BC4F7'
						}]
					}]
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData }
				});

				const positionedBars = testWrapper.vm.getPositionedBars(testData.groups[0].bars);
				expect(positionedBars).toHaveLength(1);
				expect(positionedBars[0].rowPosition).toBeDefined();
			});
		});

		describe('Zero Duration Bars', () => {
			it('should handle bars with same start and end date', () => {
				const sameDate = new Date(2025, 1, 15);
				const testData = {
					groups: [{
						title: 'Zero Duration Group',
						bars: [{
							id: 1,
							label: 'Zero Duration Bar',
							start: sameDate,
							end: sameDate,
							color: '#7BC4F7'
						}]
					}]
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: testData }
				});

				const style = testWrapper.vm.getBarGridStyle(testData.groups[0].bars[0]);
				expect(style['background-color']).toBe('#7BC4F7');
				expect(style['grid-column-start']).toBeDefined();
				expect(style['grid-column-end']).toBeDefined();
			});
		});

		describe('Empty Data Handling', () => {
			it('should handle empty groups array', () => {
				const emptyData = { groups: [] };
				
				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: emptyData }
				});

				expect(testWrapper.vm.monthColumns).toBeDefined();
				expect(testWrapper.vm.autoGeneratedLegend).toEqual([]);
			});

			it('should handle groups with empty bars arrays', () => {
				const emptyBarsData = {
					groups: [{
						title: 'Empty Group',
						bars: []
					}]
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: emptyBarsData }
				});

				expect(testWrapper.vm.getPositionedBars([])).toEqual([]);
				expect(testWrapper.vm.monthColumns.length).toBeGreaterThan(0); // Should show current year as fallback
			});
		});

		describe('Large Data Sets', () => {
			it('should handle many overlapping bars', () => {
				const manyBars = Array.from({ length: 20 }, (_, i) => ({
					id: i + 1,
					label: `Bar ${i + 1}`,
					start: new Date(2025, 0, 1 + i), // Overlapping start dates
					end: new Date(2025, 1, 1 + i),
					color: '#7BC4F7'
				}));

				const largeData = {
					groups: [{
						title: 'Many Bars Group',
						bars: manyBars
					}]
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: largeData }
				});

				const positionedBars = testWrapper.vm.getPositionedBars(manyBars);
				expect(positionedBars).toHaveLength(20);
				
				// All bars should have unique row positions
				const rowPositions = positionedBars.map(bar => bar.rowPosition);
				const uniqueRows = new Set(rowPositions);
				expect(uniqueRows.size).toBeGreaterThan(1); // Should spread across multiple rows
			});
		});

		describe('Malformed Data', () => {
			it('should handle bars without required properties', () => {
				const malformedData = {
					groups: [{
						title: 'Malformed Group',
						bars: [{
							// Missing id, label, start, end, color
						}]
					}]
				};

				const testWrapper = shallowMount(GanttChart, {
					propsData: { data: malformedData }
				});

				expect(() => testWrapper.vm.getPositionedBars(malformedData.groups[0].bars)).not.toThrow();
			});

			it('should handle groups without title', () => {
				const noTitleData = {
					groups: [{
						// Missing title
						bars: [{
							id: 1,
							label: 'Test Bar',
							start: new Date(2025, 0, 1),
							end: new Date(2025, 1, 1),
							color: '#7BC4F7'
						}]
					}]
				};

				expect(() => {
					shallowMount(GanttChart, {
						propsData: { data: noTitleData }
					});
				}).not.toThrow();
			});
		});
	});

	describe('Computed Properties', () => {
		it('should generate month columns correctly', () => {
			expect(component.monthColumns).toBeDefined();
			expect(Array.isArray(component.monthColumns)).toBe(true);
			expect(component.monthColumns.length).toBeGreaterThan(0);
		});

		it('should generate timeline grid style', () => {
			expect(component.timelineGridStyle).toBeDefined();
			expect(component.timelineGridStyle.gridTemplateColumns).toBeDefined();
		});

		it('should calculate component style with height', () => {
			expect(component.componentStyle).toBeDefined();
			expect(component.componentStyle['--gantt-content-height']).toBeDefined();
		});

		it('should calculate today column correctly', () => {
			expect(component.todayColumn).toBeDefined();
			expect(typeof component.todayColumn).toBe('number');
		});

		it('should generate tooltip position style', () => {
			component.tooltipState.x = 100;
			component.tooltipState.y = 200;
			
			const style = component.tooltipPositionStyle;
			expect(style.transform).toBe('translate(100px, 200px)');
			expect(style.zIndex).toBe(999);
		});
	});

	describe('Backward Compatibility Considerations', () => {
		it('should handle string dates correctly', () => {
			const testData = {
				groups: [
					{
						title: 'String Dates Group',
						bars: [
							{
								id: 1,
								label: 'String Date Bar',
								start: '2025-01-15',
								end: '2025-03-15',
								color: '#7BC4F7',
							},
						],
					},
				],
			};

			const testWrapper = shallowMount(GanttChart, {
				propsData: { data: testData },
			});

			expect(testWrapper.vm.monthColumns.length).toBeGreaterThan(0);
		});

		it('should handle additional bar properties', () => {
			const testData = {
				groups: [
					{
						title: 'Extended Bar Group',
						bars: [
							{
								id: 1,
								label: 'Extended Bar',
								start: new Date(2025, 0, 1),
								end: new Date(2025, 1, 1),
								color: '#7BC4F7',
								customProperty: 'custom value',
								description: 'This is a description',
							},
						],
					},
				],
			};

			const testWrapper = shallowMount(GanttChart, {
				propsData: { data: testData },
			});

			const bar = testWrapper.find('.farm-gantt-chart__bar');
			if (bar.exists()) {
				expect(testWrapper.vm.data.groups[0].bars[0].customProperty).toBe('custom value');
			}
		});
	});
});

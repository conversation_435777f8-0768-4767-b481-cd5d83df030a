# GanttChart Hybrid Tooltip System - Architecture Plan

## 🎯 **Design Overview**

This document outlines the **hybrid tooltip system** architecture for the GanttChart component, combining:

1. **Simple tooltipData approach** - for structured key-value tooltip content
2. **Flexible slot-based system** - for complex custom HTML/component tooltips
3. **Intelligent fallback** - graceful degradation when no tooltip data is provided

## 🏗️ **System Architecture**

### **1. Data Structure Design**

```mermaid
graph TB
    A[GanttBar Interface] --> B[Basic Properties]
    A --> C[tooltipData? - Optional]
    A --> D[Extensible Properties]
    
    B --> B1[id, label, start, end, color]
    
    C --> C1[Key-Value Pairs Object]
    C1 --> C2["{ 'Taxa': '1,75%', 'Vigência': '01/01/2025 - 31/12/2025' }"]
    
    D --> D1[Custom Properties for Slot Usage]
    
    E[Slot System] --> F[#tooltip Slot]
    F --> G[Receives: { bar, tooltipData }]
    
    H[Fallback Logic] --> I[tooltipData exists?]
    I --> J[Yes: Render structured tooltip]
    I --> K[No: Use slot or basic label]
```

### **2. Component API Design**

#### **2.1 Enhanced GanttBar Interface**

```typescript
export interface GanttBar {
  id: string | number;
  label: string;
  start: Date | string;
  end: Date | string;
  color: string;
  rowPosition?: number;
  
  // NEW: Tooltip data for simple cases
  tooltipData?: Record<string, string | number | null>;
  
  // Extensible for slot-based customization
  [key: string]: any;
}
```

#### **2.2 Tooltip Content Rendering Logic**

```mermaid
flowchart TD
    A[Bar Hover Event] --> B{Slot Provided?}
    B -->|Yes| C[Render Slot Content]
    B -->|No| D{tooltipData Exists?}
    D -->|Yes| E[Render Structured Tooltip]
    D -->|No| F[Render Basic Label Tooltip]
    
    C --> G[Pass { bar, tooltipData }]
    E --> H[Format Key-Value Pairs]
    F --> I[Show Only Bar Label]
    
    style C fill:#e8f5e8
    style E fill:#e8f4f8
    style F fill:#fff8e8
```

### **3. Usage Examples**

#### **3.1 Simple tooltipData Approach**

```javascript
// In your story/component data
ganttData: {
  groups: [
    {
      title: 'Campanha Safrinha 25',
      bars: [
        {
          id: 1,
          label: 'Vigência da Campanha',
          start: new Date(2025, 0, 1),
          end: new Date(2025, 5, 15),
          color: '#7BC4F7',
          // NEW: Structured tooltip data
          tooltipData: {
            'Taxa': '1,75%',
            'Vigência Campanha': '01/01/2025 a 15/06/2025',
            'Vigência Produto Comercial': '15/01/2025 a 15/05/2025',
            'Desembolso Operação': '01/03/2025 a 30/05/2025',
            'Vencimento': '01/04/2025 a 15/05/2025'
          }
        }
      ]
    }
  ]
}
```

#### **3.2 Slot-based Custom Approach**

```vue
<template>
  <farm-gantt-chart :data="ganttData">
    <template #tooltip="{ bar, tooltipData }">
      <div class="custom-tooltip">
        <div class="tooltip-header">
          <strong>{{ bar.label }}</strong>
          <span class="tooltip-badge">{{ bar.id }}</span>
        </div>
        
        <!-- Use tooltipData if available -->
        <div v-if="tooltipData" class="tooltip-content">
          <div 
            v-for="(value, key) in tooltipData" 
            :key="key"
            class="tooltip-row"
          >
            <span class="label">{{ key }}:</span>
            <span class="value">{{ value }}</span>
          </div>
        </div>
        
        <!-- Or create completely custom content -->
        <div v-else class="tooltip-content">
          <p>Período: {{ formatDateRange(bar.start, bar.end) }}</p>
          <p>Duração: {{ calculateDuration(bar.start, bar.end) }} dias</p>
          <div class="color-preview" :style="{ backgroundColor: bar.color }"></div>
        </div>
      </div>
    </template>
  </farm-gantt-chart>
</template>
```

#### **3.3 Hybrid Usage - Best of Both Worlds**

```javascript
// Mix structured tooltipData with custom slot rendering
ganttData: {
  groups: [
    {
      title: 'Financial Campaign',
      bars: [
        {
          id: 1,
          label: 'Vigência da Campanha',
          start: new Date(2025, 0, 1),
          end: new Date(2025, 5, 15),
          color: '#7BC4F7',
          tooltipData: {
            'Taxa': '1,75%',
            'Vigência': '01/01/2025 a 15/06/2025'
          },
          // Additional custom properties for slot
          riskLevel: 'Medium',
          manager: 'João Silva',
          budget: 125000
        }
      ]
    }
  ]
}
```

### **4. Technical Implementation Plan**

#### **4.1 Component Template Structure**

```vue
<template>
  <div class="farm-gantt-chart" ref="chartContainer">
    <!-- Existing chart content -->
    
    <!-- Enhanced Tooltip Container -->
    <div
      v-if="tooltipState.visible"
      class="farm-gantt-chart__tooltip-container"
      :style="tooltipPositionStyle"
    >
      <!-- Priority 1: Slot-based tooltip (highest priority) -->
      <div v-if="$slots.tooltip" class="farm-gantt-chart__tooltip">
        <slot 
          name="tooltip" 
          :bar="tooltipState.barData" 
          :tooltipData="tooltipState.barData?.tooltipData"
        />
      </div>
      
      <!-- Priority 2: Structured tooltipData (medium priority) -->
      <div 
        v-else-if="tooltipState.barData?.tooltipData" 
        class="farm-gantt-chart__tooltip farm-tooltip__popup farm-tooltip__popup--visible"
      >
        <div class="farm-tooltip__header">
          <div class="farm-tooltip__title">
            <strong>{{ tooltipState.title }}</strong>
          </div>
        </div>
        <div class="farm-tooltip__content">
          <div 
            v-for="(value, key) in tooltipState.barData.tooltipData" 
            :key="key"
            class="tooltip-data-row"
          >
            <span class="tooltip-label">{{ key }}:</span>
            <span class="tooltip-value">{{ value }}</span>
          </div>
        </div>
      </div>
      
      <!-- Priority 3: Basic fallback (lowest priority) -->
      <div 
        v-else 
        class="farm-gantt-chart__tooltip farm-tooltip__popup farm-tooltip__popup--visible"
      >
        <div class="farm-tooltip__header">
          <div class="farm-tooltip__title">
            <strong>{{ tooltipState.title }}</strong>
          </div>
        </div>
        <div class="farm-tooltip__content">
          <p>{{ formatDateRange(tooltipState.barData?.start, tooltipState.barData?.end) }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### **4.2 Enhanced Type Definitions**

```typescript
// Update to types/index.ts
export interface GanttBar {
  id: string | number;
  label: string;
  start: Date | string;
  end: Date | string;
  color: string;
  rowPosition?: number;
  
  // NEW: Tooltip system support
  tooltipData?: TooltipData;
  
  // Extensible properties
  [key: string]: any;
}

// NEW: Tooltip data interface
export interface TooltipData {
  [key: string]: string | number | null | undefined;
}

// NEW: Enhanced tooltip state
export interface TooltipState {
  visible: boolean;
  x: number;
  y: number;
  title: string;
  barData: GanttBar | null;
}
```

#### **4.3 Component Setup Function Enhancements**

```typescript
export default defineComponent({
  name: 'farm-gantt-chart',
  props: {
    data: {
      type: Object as PropType<GanttData>,
      required: true,
    },
  },
  setup(props) {
    // Enhanced tooltip state
    const tooltipState = reactive<TooltipState>({
      visible: false,
      x: 0,
      y: 0,
      title: '',
      barData: null // Now stores complete bar object
    });
    
    // Enhanced mouse event handlers
    const onBarMouseEnter = (bar: GanttBar, event: MouseEvent) => {
      tooltipState.visible = true;
      tooltipState.title = bar.label;
      tooltipState.barData = bar; // Store complete bar object
      updateTooltipPosition(event);
    };
    
    // Date formatting utility for fallback tooltip
    const formatDateRange = (start: Date | string, end: Date | string) => {
      // Implementation for date range formatting
      const startDate = typeof start === 'string' ? new Date(start) : start;
      const endDate = typeof end === 'string' ? new Date(end) : end;
      
      return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
    };
    
    return {
      tooltipState,
      tooltipPositionStyle,
      chartContainer,
      onBarMouseEnter,
      onBarMouseMove,
      onBarMouseLeave,
      formatDateRange
    };
  }
});
```

#### **4.4 Styling for Structured Tooltips**

```scss
// Add to GanttChart.scss
.farm-gantt-chart {
  &__tooltip {
    // Existing tooltip styles...
    
    .tooltip-data-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .tooltip-label {
      font-weight: 500;
      margin-right: 8px;
      color: #f5f5f5;
      opacity: 0.9;
    }
    
    .tooltip-value {
      font-weight: 600;
      color: #ffffff;
      text-align: right;
      flex-shrink: 0;
    }
  }
}
```

### **5. Story Examples for Documentation**

#### **5.1 Basic tooltipData Story**

```javascript
export const WithTooltipData = () => ({
  data() {
    return {
      ganttData: {
        groups: [
          {
            title: 'Campanha Safrinha 25',
            bars: [
              {
                id: 1,
                label: 'Vigência da Campanha',
                start: new Date(2025, 0, 1),
                end: new Date(2025, 5, 15),
                color: '#7BC4F7',
                tooltipData: {
                  'Taxa': '1,75%',
                  'Vigência Campanha': '01/01/2025 a 15/06/2025',
                  'Vigência Produto Comercial': '15/01/2025 a 15/05/2025',
                  'Desembolso Operação': '01/03/2025 a 30/05/2025',
                  'Vencimento': '01/04/2025 a 15/05/2025'
                }
              }
            ]
          }
        ]
      }
    };
  },
  template: `
    <div style="width: 100%; height: 400px; padding: 20px;">
      <h3>Tooltip with Structured Data</h3>
      <farm-gantt-chart :data="ganttData" />
    </div>
  `,
});
```

#### **5.2 Custom Slot Story**

```javascript
export const WithCustomTooltip = () => ({
  data() {
    return {
      ganttData: {
        groups: [
          {
            title: 'Custom Tooltip Demo',
            bars: [
              {
                id: 1,
                label: 'Complex Project Phase',
                start: new Date(2025, 0, 1),
                end: new Date(2025, 3, 15),
                color: '#8BB455',
                tooltipData: {
                  'Budget': '$125,000',
                  'Team Size': '8 people'
                },
                manager: 'João Silva',
                riskLevel: 'Medium',
                completionRate: 0.65
              }
            ]
          }
        ]
      }
    };
  },
  methods: {
    formatProgress(rate) {
      return `${Math.round(rate * 100)}%`;
    }
  },
  template: `
    <div style="width: 100%; height: 400px; padding: 20px;">
      <h3>Custom Slot Tooltip</h3>
      <farm-gantt-chart :data="ganttData">
        <template #tooltip="{ bar, tooltipData }">
          <div style="padding: 12px;">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
              <strong style="color: white;">{{ bar.label }}</strong>
              <span style="margin-left: auto; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 10px; font-size: 11px;">
                ID: {{ bar.id }}
              </span>
            </div>
            
            <div v-if="tooltipData" style="margin-bottom: 8px;">
              <div v-for="(value, key) in tooltipData" :key="key" style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                <span style="opacity: 0.9;">{{ key }}:</span>
                <span style="font-weight: 600;">{{ value }}</span>
              </div>
            </div>
            
            <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 8px; margin-top: 8px;">
              <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                <span style="opacity: 0.9;">Manager:</span>
                <span style="font-weight: 600;">{{ bar.manager }}</span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                <span style="opacity: 0.9;">Risk Level:</span>
                <span style="font-weight: 600; color: #FFB84D;">{{ bar.riskLevel }}</span>
              </div>
              <div style="display: flex; justify-content: space-between;">
                <span style="opacity: 0.9;">Progress:</span>
                <span style="font-weight: 600; color: #8BB455;">{{ formatProgress(bar.completionRate) }}</span>
              </div>
            </div>
          </div>
        </template>
      </farm-gantt-chart>
    </div>
  `,
});
```

### **6. Progressive Enhancement Strategy**

#### **6.1 Development Phase Approach**

```mermaid
graph LR
    A[Phase 1: Basic Tooltip] --> B[Phase 2: tooltipData Support]
    B --> C[Phase 3: Slot System]
    C --> D[Phase 4: Hybrid Integration]
    D --> E[Phase 5: Polish & Optimization]
    
    A --> A1[Existing implementation]
    B --> B1[Structured key-value rendering]
    C --> C1[Flexible slot props]
    D --> D1[Priority-based rendering]
    E --> E1[Performance & UX improvements]
```

#### **6.2 Backward Compatibility**

- ✅ Existing usage continues to work (basic label tooltips)
- ✅ Progressive enhancement - add `tooltipData` when needed
- ✅ Slot usage is completely optional
- ✅ No breaking changes to current API

### **7. Developer Experience Benefits**

#### **7.1 Three Levels of Complexity**

1. **Beginner**: Just use the component - get basic label tooltips
2. **Intermediate**: Add `tooltipData` objects for structured content
3. **Advanced**: Use slots for complete customization

#### **7.2 IntelliSense & Type Safety**

```typescript
// Developers get full autocompletion for:
// - bar properties
// - tooltipData key-value pairs
// - slot prop structure
```

## 🚀 **Implementation Benefits**

### **Why This Hybrid Approach is Optimal:**

1. **🎯 Flexibility**: Covers all use cases from simple to complex
2. **📈 Scalability**: Easy to extend without breaking existing code
3. **🧩 Reusability**: Same tooltip system works across different contexts
4. **⚡ Performance**: Efficient rendering based on available data
5. **🛠️ Developer-Friendly**: Multiple approaches for different skill levels
6. **🔧 Maintainable**: Clean separation of concerns
7. **📱 Consistent**: Follows Vue.js and component library patterns

### **Addresses Your Original Concerns:**

- ✅ **Component flexibility**: Multiple customization levels
- ✅ **Developer-friendly**: Intuitive API progression
- ✅ **Context-specific content**: Full access to bar data and context
- ✅ **Reusability**: Works across different Gantt chart implementations
- ✅ **Maintainability**: Clear architectural boundaries

## 📋 **Implementation Checklist**

### **Phase 1: Type Definitions**
- [ ] Update [`GanttBar`](types/index.ts:15) interface with `tooltipData` property
- [ ] Add `TooltipData` interface definition
- [ ] Update `TooltipState` interface to store complete bar object

### **Phase 2: Component Logic**
- [ ] Enhance tooltip state management in [`GanttChart.vue`](GanttChart.vue:129)
- [ ] Update mouse event handlers to pass complete bar object
- [ ] Add date formatting utility for fallback tooltips

### **Phase 3: Template Updates**
- [ ] Implement priority-based tooltip rendering logic
- [ ] Add slot support with proper prop passing
- [ ] Update structured tooltip rendering with key-value pairs

### **Phase 4: Styling**
- [ ] Add styles for structured tooltip data rows
- [ ] Ensure proper spacing and typography for tooltip content
- [ ] Test tooltip positioning with different content sizes

### **Phase 5: Stories & Documentation**
- [ ] Create story for basic `tooltipData` usage
- [ ] Create story for custom slot tooltips
- [ ] Create story demonstrating hybrid approach
- [ ] Update component documentation

### **Phase 6: Testing & Optimization**
- [ ] Test all three tooltip modes (basic, structured, custom)
- [ ] Verify backward compatibility
- [ ] Performance testing with complex tooltip content
- [ ] Accessibility testing for tooltip content

## 🎯 **Success Criteria**

1. **Functional**: All three tooltip modes work correctly
2. **Performance**: No degradation in chart rendering performance
3. **Usability**: Intuitive API for different developer skill levels
4. **Compatibility**: Existing implementations continue to work
5. **Extensible**: Easy to add new tooltip features in the future

This hybrid architecture provides the perfect balance of simplicity for basic use cases and flexibility for complex customization needs, making it an ideal solution for a component library that serves diverse developer requirements.
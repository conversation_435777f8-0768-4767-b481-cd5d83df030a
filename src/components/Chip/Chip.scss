@import '../../configurations/theme-colors';

.farm-chip {
    display: inline-flex;
    padding: 2px 8px 0;
    border-radius: 16px;
    height: 24px;
    min-width: 80px;
    max-width: 100%;
    text-align: center;
    align-items: center;
    color: white;
    width: 100%;
    border: 1px solid themeColor('primary');

    &--dense {
        min-width: auto;
        width: max-content;
    }

    .farm-typography {
        width: 100%;
    }

    @each $color in $theme-colors-list {
        &#{'[color=' + $color + ']'} {
            background-color: themeColor($color);
            border-color: themeColor($color);

            &.farm-chip--lighten {
                background-color: themeColor($color, 'lighten');
                border-color: themeColor($color, 'lighten');

                span {
                    color: themeColor($color, 'darken');
                }
            }

            &.farm-chip--darken {
                background-color: themeColor($color, 'darken');
                border-color: themeColor($color, 'darken');
            }
        }
    }


    &[color='neutral'] {
        span {
            color: var(--farm-secondary-green-base);
        }
        &.farm-chip--darken span {
            color: white;
        }
    }

    &[color='secondary'],
    &[color='secondary-golden'] {
        &:not(.farm-chip--outlined).farm-chip--base span {
            color: var(--farm-bw-black);
        }
    }


    &[color='secondary-green'] {
        &.farm-chip--lighten span {
            color: var(--farm-bw-white);
        }
    }

    &--outlined {
        @each $color in $theme-colors-list {
            &#{'[color=' + $color + ']'} {
                background-color: transparent;

                span {
                    color: themeColor($color);
                }

                &.farm-chip--lighten {
                    background-color: transparent;
                    border-color: themeColor($color, 'lighten');

                    span {
                        color: themeColor($color);
                    }
                }

                &.farm-chip--darken {
                    background-color: transparent;
                    border-color: themeColor($color, 'darken');

                    span {
                        color: themeColor($color, 'darken');
                    }
                }
            }
        }

        &[color='neutral'] {
            span {
                color: themeColor('secondary-green');
            }

            &.farm-chip--lighten span {
                color: themeColor('secondary-green');
            }
        }
    }
}
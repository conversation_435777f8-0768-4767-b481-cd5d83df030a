section {
	border: 2px dashed var(--farm-primary-base);
	border-radius: 2rem;
	background-color: var(--farm-primary-lighten);
	padding: 1.5rem;
	position: relative;
	min-height: 12rem;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 100%;

	input {
		opacity: 0;
		width: 100%;
		height: 100%;
		position: absolute;
		cursor: pointer;
	}

	&.highlight {
		background: var(--farm-primary-lighten);
		opacity: 0.5;
	}

	.selectfile-container {
		text-align: center;
		.farm-icon {
			font-size: 2.25rem;
			margin-bottom: 1rem;
		}
		p {
			font-weight: 600;
			color: var(--farm-bw-black-50);
		}
	}

	.reset-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		> p {
			margin-bottom: 0;
			font-size: 0.75rem;
		}
		.farm-btn {
			margin-top: 1rem;
		}
	}
}
.fileDocumentStyled {
	margin-left: 1rem;
	margin-right: 5px;
	span {
		display: block;
		font-size: 24px;
		color: var(--farm-bw-white); 
	}

	@media screen and (max-width: 600px) {
		display: none;
	}
}
.listFilesStyled {
	width: 100%;
	list-style: none;
	min-height: auto;
	margin-bottom: 20px;
	padding: 0;

	&--download {
		margin-bottom: 1rem;
		min-height: auto;
	}

	&--margin-bottom {
		min-height: 100px;
		margin-bottom: 1.25rem;
	}
}

.itemFilesStyled {
	width: 100%;
	background-color: var(--farm-bw-black-5);
	border: 1px solid var(--farm-bw-black-30);
	border-radius: 50px;
	color: var(--farm-bw-white);
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
	padding: 14px 8px;

	&:last-child {
		margin-bottom: 0;
	}

	@media screen and (max-width: 600px) {
		flex-direction: column;
	}
}

.itemFilesContentStyled {
	width: calc(100% - 24px);
	display: flex;
	align-items: center;

	p{
		margin-top: 2px;
	}

	@media screen and (max-width: 600px) {
		display: block;
		text-align: center;
	}
}

.itemFilesContentButtonStyled {
	display: flex;
	margin-right: 1rem;
	justify-content: flex-end;
	width: 15rem;
	span {
		font-size: 1.5rem;
		color: var(--farm-bw-black-50);	
		cursor: pointer;
	}

	@media screen and (max-width: 600px) {
		justify-content: center;
		margin-right: 0;
	}
}

.download-button {
	&__text {
		font-size: 0.95rem !important;
	}
	@media screen and (max-width: 600px) {
		margin-right: 0 !important;

		&__text {
			display: none;
		}
	}
}

.download-button.farm-btn {
	background-color: var(--farm-bw-black-30) !important;
	color: var(--farm-bw-black-50);
	margin-right: 1rem;

	&__content {
		color: var(--farm-bw-black-50);
	}
}

.download-button__icon {
	color: var(--farm-bw-black-50) !important;
}



.upload-icon {
	cursor: pointer;
}

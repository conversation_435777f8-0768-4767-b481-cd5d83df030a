@import '../../configurations/functions';
@import '../../configurations/mixins';

.farm-dialog__footer {
    border-top: 1px solid var(--farm-gray-lighten);
    padding: gutter('md');
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}

.farm-btn {
    margin-left: gutter('sm');
    margin-top: 0;

    .farm-icon {
        margin-right: 8px;
    }
}

@include forXsOnly {
    .farm-btn {
        margin-left: 0;
        margin-top: gutter();
    }

    .farm-dialog__footer {
        flex-direction: column;

        .farm-btn:first-of-type {
            margin-top: 0;
        }
    }
}
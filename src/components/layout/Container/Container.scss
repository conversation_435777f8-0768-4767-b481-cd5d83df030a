@import '../../../configurations/functions';
@import '../../../configurations/_mixins';

.farm-container {
    width: 100%;
    padding: 0;
    margin-right: auto;
    margin-left: auto;
    max-width: 100%;

    >div {
        border-top: 1px solid var(--farm-stroke-base);
        background-color: white;
        border-radius: 8px;
        max-width: 100%;
        margin: 40px;
        padding: gutter('lg');
        box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%), 0px 2px 2px 0px rgb(0 0 0 / 14%), 0px 1px 5px 0px rgb(0 0 0 / 12%) !important;
        overflow: hidden;

        // deprecated
        .container-main__footer-buttons-right {
            margin: 0;
            margin-top: 1rem;
            padding: 0;
            justify-content: flex-end;

            .v-btn {
                margin-left: 1rem;
            }
        }

        min-height: 128px;
    }
}
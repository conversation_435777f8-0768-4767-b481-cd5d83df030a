import { Meta } from '@storybook/addon-docs';

<Meta title="Layout/Display Breakpoints" />

# Display Breakpoints

<table width="100%">
	<thead>
		<tr>
			<th>Device</th>
			<th>Code</th>
			<th>Type</th>
			<th>Range</th>
		</tr>
	</thead>
	<tbody>
		<tr>
			<td>
				<span>
					<i className="mdi mdi-cellphone" />
					&nbsp; Extra small
				</span>
			</td>
			<td>
				<strong>xs</strong>
			</td>
			<td>Small to large phone</td>
			<td>&lt; 600px</td>
		</tr>
		<tr>
			<td>
				<i className="mdi mdi-tablet" />
				&nbsp;
				<span>Small</span>
			</td>
			<td>
				<strong>sm</strong>
			</td>
			<td>Small to medium tablet</td>
			<td>600px &gt; &lt; 960px</td>
		</tr>
		<tr>
			<td>
				<i className="mdi mdi-laptop" />
				&nbsp;
				<span>Medium</span>
			</td>
			<td>
				<strong>md</strong>
			</td>
			<td>Large tablet to laptop</td>
			<td>960px &gt; &lt; 1264px*</td>
		</tr>
		<tr>
			<td>
				<i className="mdi mdi-monitor" />
				&nbsp;
				<span>Large</span>
			</td>
			<td>
				<strong>lg</strong>
			</td>
			<td>Desktop</td>
			<td>1264px &gt; &lt; 1904px*</td>
		</tr>
		<tr>
			<td>
				<i className="mdi mdi-television" />
				&nbsp;
				<span>Extra large</span>
			</td>
			<td>
				<strong>xl</strong>
			</td>
			<td>4k and ultra-wide</td>
			<td>&gt; 1904px*</td>
		</tr>
	</tbody>
	<tfoot>
		<tr>
			<td colSpan="4">
				<em> * -16px on desktop for browser scrollbar</em>
			</td>
		</tr>
	</tfoot>
</table>

## SCSS Helpers

The _mixins.scss contains mixins for media query, to abstract the breakpoints and create queries for different breakpoints.

<code>
@import '@farm-investimentos/front-mfe-components/src/configurations/_mixins.scss';

@include fromSm { // min-width: 960px <br />
&emsp;.product-limit {<br />
&emsp;&emsp;text-align: center;<br />
&emsp;}<br />
}
</code>

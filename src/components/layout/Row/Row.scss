@import '../../../configurations/functions';
@import '../../../configurations/variables';


.farm-row {
    display: flex;
    flex-wrap: wrap;
    flex: 1 1 auto;
    
    margin: 0 -#{gutter('vuetify')};

    @each $k in $aligns {
        &#{'--align-' + $k} {
            align-items: $k!important;
        }
    }

    @each $k in $align-contents {
        &#{'--align-content-' + $k} {
            align-content: $k!important;
        }
    }

    @each $k in $justifications {
        &#{'--justify-' + $k} {
            justify-content: $k!important;
        }
    }

    &--no-default-gutters {
        margin: 0;
    }

    &--extra-decrease {
        margin: 0 -#{2 * gutter('vuetify')};
    }

    &--y-grid-gutters > .farm-col {
        margin-top: 12px;
        margin-bottom: 12px;
    }
}